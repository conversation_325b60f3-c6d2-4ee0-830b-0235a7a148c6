import pymem
import pymem.process
import time
import dearpygui.dearpygui as dpg
import threading

# --- Config ---
PROCESS_NAME = "TheSpellBrigade.exe"
GOLD_BASE_OFFSET = 0x3858F00
LEVEL_BASE_OFFSET = 0x385CDE8
GOLD_OFFSETS = [0x20, 0xC0, 0x10, 0xB8, 0x0, 0x38]
LEVEL_OFFSETS = [0x90, 0x10, 0x10, 0x58, 0x48, 0x40]
# --------------

# Global state dictionary
app_state = {
    "pm": None,
    "module_base": 0,
    "game_connected": False,
    "gold_address": 0,
    "level_address": 0,
    "status_text": "Please start the game, then click 'Connect to Game'.",
    "current_gold": "N/A",
    "current_level": "N/A",
    "gold_locked": False,
    "level_locked": False
}

def find_pointer_address(pm, base, offsets):
    """
    Calculates the final address from a base address and multi-level offsets (64-bit).
    """
    try:
        addr = pm.read_longlong(base)
        if addr == 0: return None
        
        for offset in offsets[:-1]:
            addr = pm.read_longlong(addr + offset)
            if addr == 0: return None
            
        return addr + offsets[-1]
    except Exception:
        return None

def connect_to_game():
    """
    Runs in a background thread to connect to the game process.
    """
    app_state["game_connected"] = False
    app_state["status_text"] = f"Searching for game process: {PROCESS_NAME}..."
    
    try:
        app_state["pm"] = pymem.Pymem(PROCESS_NAME)
        app_state["module_base"] = pymem.process.module_from_name(
            app_state["pm"].process_handle, "GameAssembly.dll"
        ).lpBaseOfDll
        
        app_state["game_connected"] = True
        app_state["status_text"] = f"Successfully connected! Process ID: {app_state['pm'].process_id}"
        print(f"[+] GameAssembly.dll module base: {hex(app_state['module_base'])}")

    except pymem.exception.ProcessNotFound:
        app_state["status_text"] = f"Process '{PROCESS_NAME}' not found. Please start the game first."
    except Exception as e:
        app_state["status_text"] = f"Error during connection: {e}"

def start_connection_thread():
    """Starts the connection thread."""
    threading.Thread(target=connect_to_game, daemon=True).start()

def set_gold_value():
    """
    Callback for the 'Set Gold' button.
    """
    if not app_state["game_connected"] or not app_state["gold_address"]:
        app_state["status_text"] = "Please connect to the game and ensure gold address is valid."
        return

    try:
        new_gold = dpg.get_value("new_gold_input")
        app_state["pm"].write_int(app_state["gold_address"], new_gold)
        
        # Verify write
        time.sleep(0.1)
        written_gold = app_state["pm"].read_int(app_state["gold_address"])
        if written_gold == new_gold:
            app_state["status_text"] = f"Success! Gold has been set to: {new_gold}"
            app_state["current_gold"] = str(new_gold)
        else:
            app_state["status_text"] = "Write operation failed!"
            
    except Exception as e:
        app_state["status_text"] = f"Error setting gold: {e}"

def set_level_value():
    """
    Callback for the 'Set Level' button.
    """
    if not app_state["game_connected"] or not app_state["level_address"]:
        app_state["status_text"] = "Please connect to the game and ensure level address is valid."
        return

    try:
        new_level = dpg.get_value("new_level_input")
        app_state["pm"].write_int(app_state["level_address"], new_level)
        
        # Verify write
        time.sleep(0.1)
        written_level = app_state["pm"].read_int(app_state["level_address"])
        if written_level == new_level:
            app_state["status_text"] = f"Success! Level has been set to: {new_level}"
            app_state["current_level"] = str(new_level)
        else:
            app_state["status_text"] = "Write operation failed!"
            
    except Exception as e:
        app_state["status_text"] = f"Error setting level: {e}"

def create_gui():
    """
    Creates the Dear PyGui interface.
    """
    dpg.create_context()

    with dpg.window(label="The Spell Brigade Cheat by Roo", width=450, height=320, no_resize=True):
        dpg.add_text("", tag="status_label")
        dpg.add_separator()
        
        dpg.add_button(label="Connect to Game", callback=start_connection_thread)

        with dpg.group(horizontal=True):
            dpg.add_text("Current Gold:", tag="current_gold_label_prefix")
            dpg.add_text("N/A", tag="current_gold_label")
            dpg.add_spacer(width=20)
            dpg.add_text("Current Level:", tag="current_level_label_prefix")
            dpg.add_text("N/A", tag="current_level_label")

        dpg.add_separator()
        
        with dpg.group(horizontal=True):
            dpg.add_input_int(label="Target Gold", tag="new_gold_input", default_value=99999, width=200)
            dpg.add_button(label="Set Gold", callback=set_gold_value, width=100)
            dpg.add_checkbox(label="Lock Gold", tag="gold_lock_checkbox", callback=lambda: app_state.update({"gold_locked": dpg.get_value("gold_lock_checkbox")}))

        with dpg.group(horizontal=True):
            dpg.add_input_int(label="Target Level", tag="new_level_input", default_value=100, width=200)
            dpg.add_button(label="Set Level", callback=set_level_value, width=100)
            dpg.add_checkbox(label="Lock Level", tag="level_lock_checkbox", callback=lambda: app_state.update({"level_locked": dpg.get_value("level_lock_checkbox")}))
        
        dpg.add_spacer(height=10)
        dpg.add_text("Hint: Please run this program as an administrator.", color=(255, 255, 0))


    dpg.create_viewport(title='The Spell Brigade Cheat', width=450, height=320)
    dpg.setup_dearpygui()
    dpg.show_viewport()

def main_loop():
    """
    Main render loop to update the UI.
    """
    while dpg.is_dearpygui_running():
        # Update UI elements
        dpg.set_value("status_label", app_state["status_text"])
        
        if app_state["game_connected"]:
            # Calculate gold address
            gold_addr = find_pointer_address(
                app_state["pm"],
                app_state["module_base"] + GOLD_BASE_OFFSET,
                GOLD_OFFSETS
            )
            app_state["gold_address"] = gold_addr

            if gold_addr:
                try:
                    if app_state["gold_locked"]:
                        locked_gold_value = dpg.get_value("new_gold_input")
                        app_state["pm"].write_int(gold_addr, locked_gold_value)
                        app_state["current_gold"] = str(locked_gold_value) + " (Locked)"
                    else:
                        current_gold = app_state["pm"].read_int(gold_addr)
                        app_state["current_gold"] = str(current_gold)
                except Exception:
                    app_state["current_gold"] = "Read/Write Error"
            else:
                app_state["current_gold"] = "Invalid Address"
            
            # Calculate level address
            level_addr = find_pointer_address(
                app_state["pm"],
                app_state["module_base"] + LEVEL_BASE_OFFSET,
                LEVEL_OFFSETS
            )
            app_state["level_address"] = level_addr

            if level_addr:
                try:
                    if app_state["level_locked"]:
                        locked_level_value = dpg.get_value("new_level_input")
                        app_state["pm"].write_int(level_addr, locked_level_value)
                        app_state["current_level"] = str(locked_level_value) + " (Locked)"
                    else:
                        current_level = app_state["pm"].read_int(level_addr)
                        app_state["current_level"] = str(current_level)
                except Exception:
                    app_state["current_level"] = "Read/Write Error"
            else:
                app_state["current_level"] = "Invalid Address"

        dpg.set_value("current_gold_label", app_state["current_gold"])
        dpg.set_value("current_level_label", app_state["current_level"])
        
        dpg.render_dearpygui_frame()
        time.sleep(0.05) # Reduce CPU usage

    dpg.destroy_context()

if __name__ == "__main__":
    create_gui()
    main_loop()
